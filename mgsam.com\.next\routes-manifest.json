{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:file((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/]+\\.\\w+)/", "destination": "/:file", "internal": true, "missing": [{"type": "header", "key": "x-nextjs-data"}], "statusCode": 308, "regex": "^(?:/((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/]+\\.\\w+))/$"}, {"source": "/:notfile((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/\\.]+)", "destination": "/:notfile/", "internal": true, "statusCode": 308, "regex": "^(?:/((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/\\.]+))$"}], "headers": [], "dynamicRoutes": [], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/arasti<PERSON>-alan<PERSON>i", "regex": "^/arastirma\\-alan<PERSON>i(?:/)?$", "routeKeys": {}, "namedRegex": "^/arastirma\\-alan<PERSON>i(?:/)?$"}, {"page": "/control-area/dashboard/articles", "regex": "^/control\\-area/dashboard/articles(?:/)?$", "routeKeys": {}, "namedRegex": "^/control\\-area/dashboard/articles(?:/)?$"}, {"page": "/farkliliklarimiz", "regex": "^/farkliliklarimiz(?:/)?$", "routeKeys": {}, "namedRegex": "^/farkliliklarimiz(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/hakkimizda", "regex": "^/hakkimizda(?:/)?$", "routeKeys": {}, "namedRegex": "^/hakkimizda(?:/)?$"}, {"page": "/hedef-ve-il<PERSON><PERSON><PERSON>z", "regex": "^/hedef\\-ve\\-il<PERSON><PERSON><PERSON>z(?:/)?$", "routeKeys": {}, "namedRegex": "^/hedef\\-ve\\-il<PERSON><PERSON><PERSON>z(?:/)?$"}, {"page": "/iletisim", "regex": "^/iletisim(?:/)?$", "routeKeys": {}, "namedRegex": "^/iletisim(?:/)?$"}, {"page": "/makaleler", "regex": "^/makaleler(?:/)?$", "routeKeys": {}, "namedRegex": "^/makaleler(?:/)?$"}, {"page": "/robots.txt", "regex": "^/robots\\.txt(?:/)?$", "routeKeys": {}, "namedRegex": "^/robots\\.txt(?:/)?$"}, {"page": "/sitemap.xml", "regex": "^/sitemap\\.xml(?:/)?$", "routeKeys": {}, "namedRegex": "^/sitemap\\.xml(?:/)?$"}, {"page": "/toplanti-planla", "regex": "^/toplanti\\-planla(?:/)?$", "routeKeys": {}, "namedRegex": "^/toplanti\\-planla(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}