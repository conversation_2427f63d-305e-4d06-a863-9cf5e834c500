(()=>{var e={};e.id=746,e.ids=[746],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7462:(e,t,r)=>{"use strict";r.d(t,{Rn:()=>u,_7:()=>o});var s=r(46101);r(85663);let a={host:process.env.DB_HOST||"localhost",user:process.env.DB_USER||"metaanalizgroup_gr",password:process.env.DB_PASSWORD||"Mah2025!",database:process.env.DB_NAME||"metaanalizgroup_meta",charset:process.env.DB_CHARSET||"utf8mb4",waitForConnections:!0,connectionLimit:10,queueLimit:0},n=s.createPool(a);async function i(e,t=[]){try{let r=await n.getConnection(),[s]=await r.execute(e,t);return r.release(),s}catch(e){throw e}}async function o(e){let t=`
    INSERT INTO contact_messages (site_id, source, name, email, phone, company, message, created_at, status, ip_address, user_agent)
    VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), 'new', ?, ?)
  `,r=[2,"mgsam",e.name,e.email,e.phone||null,e.company||null,e.message,null,null];try{return await i(t,r)}catch(e){throw e}}async function u(e){let t=`
    INSERT INTO meeting_requests (site_id, source, name, email, phone, company, service, meeting_date, meeting_time, message, created_at, status, ip_address, user_agent, notes)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), 'pending', ?, ?, ?)
  `,r=[2,"mgsam",e.name,e.email,e.phone||null,e.company||null,e.service,e.meeting_date,e.meeting_time,e.message||null,null,null,null];try{return await i(t,r)}catch(e){throw e}}},9161:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>l,serverHooks:()=>x,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>d});var s={};r.r(s),r.d(s,{POST:()=>p,dynamic:()=>c});var a=r(96559),n=r(48088),i=r(37719),o=r(32190),u=r(7462);let c="force-static";async function p(e){try{let t=await e.json(),{name:r,email:s,message:a}=t;if(!r||!s||!a)return o.NextResponse.json({error:"Gerekli alanlar eksik"},{status:400});if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(s))return o.NextResponse.json({error:"Ge\xe7ersiz email formatı"},{status:400});return await (0,u._7)({name:r.trim(),email:s.trim().toLowerCase(),phone:t.phone?.trim()||null,company:t.company?.trim()||null,message:a.trim()}),o.NextResponse.json({message:"Mesajınız başarıyla g\xf6nderildi. En kısa s\xfcrede size d\xf6n\xfcş yapacağız."},{status:200})}catch(e){return o.NextResponse.json({error:"Mesaj g\xf6nderilirken bir hata oluştu. L\xfctfen tekrar deneyin."},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/contact/route",pathname:"/api/contact",filename:"route",bundlePath:"app/api/contact/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\mgsam.com\\app\\api\\contact\\route.ts",nextConfigOutput:"export",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:d,serverHooks:x}=l;function g(){return(0,i.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:d})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19771:e=>{"use strict";e.exports=require("process")},27910:e=>{"use strict";e.exports=require("stream")},28303:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=28303,e.exports=t},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},41204:e=>{"use strict";e.exports=require("string_decoder")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66136:e=>{"use strict";e.exports=require("timers")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580,179],()=>r(9161));module.exports=s})();