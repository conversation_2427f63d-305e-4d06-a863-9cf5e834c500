(()=>{var e={};e.id=285,e.ids=[285],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},86466:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>s});var t=a(37413);function s(){return(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{children:"Articles Dashboard"}),(0,t.jsx)("p",{children:"Articles management page"})]})}},96487:()=>{},99637:(e,r,a)=>{"use strict";a.r(r),a.d(r,{GlobalError:()=>i.default,__next_app__:()=>p,pages:()=>d,routeModule:()=>m,tree:()=>l});var t=a(65239),s=a(48088),i=a(46076),o=a(30893),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);a.d(r,n);let l={children:["",{children:["control-area",{children:["dashboard",{children:["articles",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,86466)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\mgsam.com\\app\\control-area\\dashboard\\articles\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,58014)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\mgsam.com\\app\\layout.tsx"],error:[()=>Promise.resolve().then(a.bind(a,52608)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\mgsam.com\\app\\error.tsx"],loading:[()=>Promise.resolve().then(a.bind(a,99766)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\mgsam.com\\app\\loading.tsx"],"global-error":[()=>Promise.resolve().then(a.bind(a,46076)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\mgsam.com\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,82366)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\mgsam.com\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\mgsam.com\\app\\control-area\\dashboard\\articles\\page.tsx"],p={require:a,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/control-area/dashboard/articles/page",pathname:"/control-area/dashboard/articles",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})}};var r=require("../../../../webpack-runtime.js");r.C(e);var a=e=>r(r.s=e),t=r.X(0,[447,764,410],()=>a(99637));module.exports=t})();