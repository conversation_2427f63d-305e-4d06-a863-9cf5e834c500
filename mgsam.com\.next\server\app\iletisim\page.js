(()=>{var e={};e.id=157,e.ids=[157],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23665:(e,t,s)=>{Promise.resolve().then(s.bind(s,93170))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},42872:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var a=s(60687),r=s(43210),i=s(95188),l=s(19386);function n(){let[e,t]=(0,r.useState)({name:"",email:"",phone:"",company:"",message:""}),[s,n]=(0,r.useState)(!1),[o,d]=(0,r.useState)(""),[m,c]=(0,r.useState)(!1),x=async s=>{s.preventDefault(),n(!0),d("");try{let s=await fetch("/api/contact.php",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),a=await s.json();a.success?(c(!0),d(a.message),t({name:"",email:"",phone:"",company:"",message:""})):(c(!1),d(a.message))}catch(e){c(!1),d("Bir hata oluştu. L\xfctfen daha sonra tekrar deneyin.")}finally{n(!1)}},p=s=>{t({...e,[s.target.name]:s.target.value})};return(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-white flex flex-col",children:[(0,a.jsx)(i.A,{currentPage:"iletisim"}),(0,a.jsx)("main",{className:"flex-1 pt-20 pb-8",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto flex flex-col",children:[(0,a.jsxs)("div",{className:"text-center py-4 hidden md:block",children:[(0,a.jsxs)("div",{className:"inline-flex items-center px-3 py-1 bg-slate-100 rounded-full text-slate-700 text-sm font-medium mb-3",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-gradient-to-r from-green-500 to-green-400 rounded-full mr-2"})," ","Bizimle İletişime Ge\xe7in"]}),(0,a.jsxs)("h1",{className:"text-3xl md:text-4xl font-extralight text-slate-900 mb-2 tracking-tight",children:["Profesyonel"," ",(0,a.jsx)("span",{className:"text-slate-700 font-light",children:"Danışmanlık"})]}),(0,a.jsx)("p",{className:"text-slate-600 font-light text-sm",children:"Kurumsal \xe7\xf6z\xfcmleriniz i\xe7in uzman ekibimizle g\xf6r\xfcş\xfcn"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 py-8",children:[(0,a.jsxs)("div",{className:"bg-white rounded-3xl p-6 shadow-lg border border-slate-200/50 relative overflow-hidden lg:order-2",children:[(0,a.jsx)("div",{className:"absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-slate-100/30 to-transparent rounded-full -translate-y-16 translate-x-16"}),(0,a.jsxs)("div",{className:"relative z-10 h-full flex flex-col",children:[(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("h2",{className:"text-xl font-light text-slate-900 mb-1 tracking-tight",children:"Mesaj G\xf6nder"}),(0,a.jsx)("p",{className:"text-slate-600 text-sm font-light",children:"24 saat i\xe7inde profesyonel yanıt alın"})]}),(0,a.jsxs)("form",{onSubmit:x,className:"flex-1 flex flex-col space-y-3",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:[(0,a.jsx)("div",{children:(0,a.jsx)("input",{type:"text",name:"name",placeholder:"Adınız Soyadınız *",value:e.name,onChange:p,required:!0,className:"w-full px-4 py-2.5 border border-slate-200 rounded-xl focus:ring-2 focus:ring-slate-400 focus:border-transparent transition-all duration-200 text-sm font-light"})}),(0,a.jsx)("div",{children:(0,a.jsx)("input",{type:"email",name:"email",placeholder:"E-posta Adresiniz *",value:e.email,onChange:p,required:!0,className:"w-full px-4 py-2.5 border border-slate-200 rounded-xl focus:ring-2 focus:ring-slate-400 focus:border-transparent transition-all duration-200 text-sm font-light"})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:[(0,a.jsx)("div",{children:(0,a.jsx)("input",{type:"tel",name:"phone",placeholder:"Telefon Numaranız",value:e.phone,onChange:p,className:"w-full px-4 py-2.5 border border-slate-200 rounded-xl focus:ring-2 focus:ring-slate-400 focus:border-transparent transition-all duration-200 text-sm font-light"})}),(0,a.jsx)("div",{children:(0,a.jsx)("input",{type:"text",name:"company",placeholder:"Şirket Adınız *",value:e.company,onChange:p,required:!0,className:"w-full px-4 py-2.5 border border-slate-200 rounded-xl focus:ring-2 focus:ring-slate-400 focus:border-transparent transition-all duration-200 text-sm font-light"})})]}),(0,a.jsx)("div",{children:(0,a.jsx)("textarea",{name:"message",placeholder:"Mesajınız *",value:e.message,onChange:p,required:!0,rows:4,className:"w-full px-4 py-2.5 border border-slate-200 rounded-xl focus:ring-2 focus:ring-slate-400 focus:border-transparent transition-all duration-200 text-sm font-light resize-none"})}),o&&(0,a.jsx)("div",{className:`p-3 rounded-xl text-sm font-medium ${m?"bg-green-50 text-green-700 border border-green-200":"bg-red-50 text-red-700 border border-red-200"}`,children:o}),(0,a.jsx)("button",{type:"submit",disabled:s,className:`w-full py-2.5 px-6 rounded-xl font-medium transition-all duration-300 transform shadow-lg text-sm ${s?"bg-slate-400 text-white cursor-not-allowed":"bg-gradient-to-r from-slate-900 to-slate-700 text-white hover:from-slate-800 hover:to-slate-600 hover:-translate-y-1 hover:shadow-xl"}`,children:s?"G\xf6nderiliyor...":"Mesajı G\xf6nder"})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700 rounded-3xl p-6 text-white relative overflow-hidden lg:order-1",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width=%2240%22 height=%2240%22 viewBox=%220 0 40 40%22 xmlns=%22http://www.w3.org/2000/svg%22%3E%3Cg fill=%22none%22 fill-rule=%22evenodd%22%3E%3Cg fill=%22%23ffffff%22 fill-opacity=%220.03%22%3E%3Cpath d=%22m0 40l40-40h-40z%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-30"}),(0,a.jsxs)("div",{className:"relative z-10 h-full flex flex-col",children:[(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("h2",{className:"text-xl font-light text-white mb-1 tracking-tight",children:"İletişim Bilgileri"}),(0,a.jsx)("p",{className:"text-slate-300 text-sm font-light",children:"Hızlı ve profesyonel yanıt garantisi"})]}),(0,a.jsxs)("div",{className:"flex-1 space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-slate-700/50 backdrop-blur-sm rounded-2xl flex items-center justify-center flex-shrink-0 border border-slate-600/30",children:(0,a.jsxs)("svg",{className:"w-5 h-5 text-slate-200",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-white mb-1 text-sm",children:"Merkez Ofis"}),(0,a.jsxs)("p",{className:"text-slate-300 text-sm font-light",children:["Yenicami Mah. \xd6zmen Sok. No: 24/A",(0,a.jsx)("br",{}),"S\xf6ke / Aydın"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-slate-700/50 backdrop-blur-sm rounded-2xl flex items-center justify-center flex-shrink-0 border border-slate-600/30",children:(0,a.jsx)("svg",{className:"w-5 h-5 text-slate-200",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-white mb-1 text-sm",children:"Direkt Hat"}),(0,a.jsx)("p",{className:"text-slate-300 text-sm font-light",children:"+90 (542) 380 00 50"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-slate-700/50 backdrop-blur-sm rounded-2xl flex items-center justify-center flex-shrink-0 border border-slate-600/30",children:(0,a.jsx)("svg",{className:"w-5 h-5 text-slate-200",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-white mb-1 text-sm",children:"E-posta"}),(0,a.jsx)("p",{className:"text-slate-300 text-sm font-light",children:"<EMAIL>"}),(0,a.jsx)("p",{className:"text-slate-300 text-sm font-light",children:"<EMAIL>"})]})]})]})]})]})]})]})})}),(0,a.jsx)("section",{className:"w-full h-80",children:(0,a.jsx)("iframe",{src:"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3141.8947!2d27.3236!3d37.7507!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x14bfd3c5e5e5e5e5%3A0x5e5e5e5e5e5e5e5e!2sYenicami%20Mah.%20%C3%96zmen%20Sok.%20No%3A24%2FA%2C%2009270%20S%C3%B6ke%2FAyd%C4%B1n!5e0!3m2!1str!2str!4v1640995200000!5m2!1str!2str",width:"100%",height:"100%",style:{border:0},allowFullScreen:!0,loading:"lazy",referrerPolicy:"no-referrer-when-downgrade",title:"Meta Analiz Group Ofis Konumu - Yenicami Mah. \xd6zmen Sok. No: 24/A S\xf6ke, Aydın"})}),(0,a.jsx)(l.A,{})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},84861:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.default,__next_app__:()=>m,pages:()=>d,routeModule:()=>c,tree:()=>o});var a=s(65239),r=s(48088),i=s(46076),l=s(30893),n={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);s.d(t,n);let o={children:["",{children:["iletisim",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,93170)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\mgsam.com\\app\\iletisim\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,58014)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\mgsam.com\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,52608)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\mgsam.com\\app\\error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,99766)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\mgsam.com\\app\\loading.tsx"],"global-error":[()=>Promise.resolve().then(s.bind(s,46076)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\mgsam.com\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,82366)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\mgsam.com\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\mgsam.com\\app\\iletisim\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},c=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/iletisim/page",pathname:"/iletisim",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},88817:(e,t,s)=>{Promise.resolve().then(s.bind(s,42872))},93170:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Meta A\\\\metaanalizmusavirlik\\\\metaanalizgorup_website\\\\mgsam.com\\\\app\\\\iletisim\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\mgsam.com\\app\\iletisim\\page.tsx","default")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[447,764,410,197],()=>s(84861));module.exports=a})();