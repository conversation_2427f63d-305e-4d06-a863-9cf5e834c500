exports.id=410,exports.ids=[410],exports.modules={2586:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});var s=a(60687);function r({error:e,reset:t}){return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,s.jsxs)("div",{className:"max-w-md w-full bg-white shadow-lg rounded-lg p-6",children:[(0,s.jsx)("div",{className:"flex items-center justify-center w-12 h-12 mx-auto bg-red-100 rounded-full",children:(0,s.jsx)("svg",{className:"w-6 h-6 text-red-600",fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,s.jsxs)("div",{className:"mt-3 text-center",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Bir hata oluştu"}),(0,s.jsx)("div",{className:"mt-2 px-7 py-3",children:(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Sayfa y\xfcklenirken beklenmeyen bir hata oluştu. L\xfctfen tekrar deneyin."})}),(0,s.jsx)("div",{className:"items-center px-4 py-3",children:(0,s.jsx)("button",{onClick:t,className:"px-4 py-2 bg-primary text-white text-base font-medium rounded-md w-full shadow-sm hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary",children:"Tekrar Dene"})})]})]})})}a(43210)},2710:()=>{},7792:(e,t,a)=>{Promise.resolve().then(a.bind(a,19550))},8776:(e,t,a)=>{Promise.resolve().then(a.bind(a,82366))},9617:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,86346,23)),Promise.resolve().then(a.t.bind(a,27924,23)),Promise.resolve().then(a.t.bind(a,35656,23)),Promise.resolve().then(a.t.bind(a,40099,23)),Promise.resolve().then(a.t.bind(a,38243,23)),Promise.resolve().then(a.t.bind(a,28827,23)),Promise.resolve().then(a.t.bind(a,62763,23)),Promise.resolve().then(a.t.bind(a,97173,23))},10289:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,16444,23)),Promise.resolve().then(a.t.bind(a,16042,23)),Promise.resolve().then(a.t.bind(a,88170,23)),Promise.resolve().then(a.t.bind(a,49477,23)),Promise.resolve().then(a.t.bind(a,29345,23)),Promise.resolve().then(a.t.bind(a,12089,23)),Promise.resolve().then(a.t.bind(a,46577,23)),Promise.resolve().then(a.t.bind(a,31307,23))},10684:(e,t,a)=>{Promise.resolve().then(a.bind(a,99766))},19550:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});var s=a(60687);function r({error:e,reset:t}){return(0,s.jsx)("html",{children:(0,s.jsx)("body",{children:(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,s.jsxs)("div",{className:"max-w-md w-full bg-white shadow-lg rounded-lg p-6",children:[(0,s.jsx)("div",{className:"flex items-center justify-center w-12 h-12 mx-auto bg-red-100 rounded-full",children:(0,s.jsx)("svg",{className:"w-6 h-6 text-red-600",fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,s.jsxs)("div",{className:"mt-3 text-center",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Sistem Hatası"}),(0,s.jsx)("div",{className:"mt-2 px-7 py-3",children:(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Uygulama d\xfczeyinde bir hata oluştu. Sayfayı yenileyin."})}),(0,s.jsx)("div",{className:"items-center px-4 py-3",children:(0,s.jsx)("button",{onClick:t,className:"px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md w-full shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500",children:"Tekrar Dene"})})]})]})})})})}},37140:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>n});var s=a(60687),r=a(85814),i=a.n(r);function n(){return(0,s.jsx)("div",{className:"min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-slate-50 to-white",children:(0,s.jsxs)("div",{className:"text-center p-8 bg-white rounded-lg shadow-lg border border-slate-200",children:[(0,s.jsx)("h1",{className:"text-6xl font-light text-slate-900 mb-4",children:"404"}),(0,s.jsx)("h2",{className:"text-2xl font-light text-slate-700 mb-4",children:"Sayfa Bulunamadı"}),(0,s.jsx)("p",{className:"text-slate-600 mb-8 font-light",children:"Aradığınız sayfa mevcut değil veya taşınmış olabilir."}),(0,s.jsx)(i(),{href:"/",className:"inline-block px-8 py-3 bg-slate-900 text-white rounded-xl hover:bg-slate-800 transition-colors duration-300 font-medium",children:"Ana Sayfaya D\xf6n"})]})})}},46055:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});var s=a(31658);let r=async e=>[{type:"image/x-icon",sizes:"2844x2867",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},46076:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Meta A\\\\metaanalizmusavirlik\\\\metaanalizgorup_website\\\\mgsam.com\\\\app\\\\global-error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\mgsam.com\\app\\global-error.tsx","default")},48774:(e,t,a)=>{Promise.resolve().then(a.bind(a,2586))},52608:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Meta A\\\\metaanalizmusavirlik\\\\metaanalizgorup_website\\\\mgsam.com\\\\app\\\\error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\mgsam.com\\app\\error.tsx","default")},52812:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>n});var s=a(60687),r=a(30474),i=a(43210);function n(){let[e,t]=(0,i.useState)(!1);return e?(0,s.jsxs)("div",{className:"fixed inset-0 bg-white flex flex-col items-center justify-center z-50",children:[(0,s.jsx)("div",{className:"relative mb-8",children:(0,s.jsx)(r.default,{src:"/meta_group_logo.webp",alt:"Meta Analiz Group Logo",width:240,height:60,className:"animate-pulse"})}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"text-2xl text-slate-600 font-light",children:"Y\xfckleniyor"}),(0,s.jsxs)("div",{className:"flex space-x-1",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-slate-600 rounded-full animate-[bounce_1s_infinite_-0.3s]"}),(0,s.jsx)("div",{className:"w-2 h-2 bg-slate-600 rounded-full animate-[bounce_1s_infinite_-0.2s]"}),(0,s.jsx)("div",{className:"w-2 h-2 bg-slate-600 rounded-full animate-[bounce_1s_infinite]"})]})]})]}):null}},58014:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>o,metadata:()=>l,viewport:()=>n});var s=a(37413),r=a(13346),i=a.n(r);a(82704);let n={width:"device-width",initialScale:1},l={metadataBase:new URL("https://mgsam.com"),title:"MGSAM - Meta Global Stratejiler Ekonomik ve Siyasal Araştırmalar Merkezi",description:"Bağımsız d\xfcş\xfcnce kuruluşu olarak kamu kurum ve kuruluşlarının karar alma s\xfcre\xe7lerine bilimsel bilgi ve stratejik analizlerle destek oluyoruz. Doğru bilgi, iyi y\xf6netim, s\xfcrd\xfcr\xfclebilir kalkınma.",keywords:"think tank, d\xfcş\xfcnce kuruluşu, ekonomik araştırmalar, siyasal araştırmalar, politika geliştirme, stratejik analiz, kamu politikası, jeopolitik, savunma sanayi, ulusal g\xfcvenlik, MGSAM",authors:[{name:"Dr. (Ph.D.) \xd6zg\xfcr Atacan",url:"https://mgsam.com"}],robots:"index, follow",alternates:{canonical:"https://mgsam.com"},verification:{google:"google-site-verification-code"},icons:{icon:[{url:"/mgsam.png",sizes:"32x32",type:"image/png"},{url:"/mgsam.png",sizes:"16x16",type:"image/png"}],apple:[{url:"/mgsam.png",sizes:"180x180",type:"image/png"}],shortcut:"/mgsam.png"},openGraph:{title:"MGSAM - Meta Global Stratejiler Ekonomik ve Siyasal Araştırmalar Merkezi",description:"T\xfcrkiye'den t\xfcm d\xfcnyaya ekonomik ve siyasal araştırmalar merkezi. Yaşamın Kalitesi, Y\xf6netimin Kalitesi ile Artar.",url:"https://mgsam.com",siteName:"MGSAM",images:[{url:"/mgsam_logo.webp",width:1200,height:630,alt:"MGSAM Logo"}],locale:"tr_TR",type:"website"},twitter:{card:"summary_large_image",title:"MGSAM - Meta Global Stratejiler Ekonomik ve Siyasal Araştırmalar Merkezi",description:"T\xfcrkiye'den t\xfcm d\xfcnyaya ekonomik ve siyasal araştırmalar merkezi. Yaşamın Kalitesi, Y\xf6netimin Kalitesi ile Artar.",images:["/mgsam_logo.webp"]},other:{"theme-color":"#1e293b","color-scheme":"light","format-detection":"telephone=no"}};function o({children:e}){return(0,s.jsxs)("html",{lang:"tr",className:i().variable,children:[(0,s.jsx)("head",{}),(0,s.jsx)("body",{className:"font-inter antialiased",children:e})]})}},62958:()=>{},66078:(e,t,a)=>{Promise.resolve().then(a.bind(a,52608))},72328:(e,t,a)=>{Promise.resolve().then(a.bind(a,37140))},72944:(e,t,a)=>{Promise.resolve().then(a.bind(a,46076))},82366:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Meta A\\\\metaanalizmusavirlik\\\\metaanalizgorup_website\\\\mgsam.com\\\\app\\\\not-found.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\mgsam.com\\app\\not-found.tsx","default")},82704:()=>{},97116:(e,t,a)=>{Promise.resolve().then(a.bind(a,52812))},99766:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Meta A\\\\metaanalizmusavirlik\\\\metaanalizgorup_website\\\\mgsam.com\\\\app\\\\loading.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\mgsam.com\\app\\loading.tsx","default")}};