(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[39],{1410:(e,s,r)=>{Promise.resolve().then(r.bind(r,6678))},6678:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>i});var t=r(5155),n=r(2115);function i(e){let{error:s,reset:r}=e;return(0,n.useEffect)(()=>{},[s]),(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,t.jsxs)("div",{className:"max-w-md w-full bg-white shadow-lg rounded-lg p-6",children:[(0,t.jsx)("div",{className:"flex items-center justify-center w-12 h-12 mx-auto bg-red-100 rounded-full",children:(0,t.jsx)("svg",{className:"w-6 h-6 text-red-600",fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,t.jsxs)("div",{className:"mt-3 text-center",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Bir hata oluştu"}),(0,t.jsx)("div",{className:"mt-2 px-7 py-3",children:(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Sayfa y\xfcklenirken beklenmeyen bir hata oluştu. L\xfctfen tekrar deneyin."})}),(0,t.jsx)("div",{className:"items-center px-4 py-3",children:(0,t.jsx)("button",{onClick:r,className:"px-4 py-2 bg-primary text-white text-base font-medium rounded-md w-full shadow-sm hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary",children:"Tekrar Dene"})})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[441,684,358],()=>s(1410)),_N_E=e.O()}]);