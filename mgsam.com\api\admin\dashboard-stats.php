<?php
require_once '../config.php';

// Check admin authentication
session_start();
if (!isset($_SESSION['admin_user'])) {
    http_response_code(401);
    sendResponse(false, 'Yet<PERSON><PERSON> erişim');
}

$pdo = getDBConnection();

if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    try {
        // Get contact stats (from both contact_forms and contact_messages)
        $contactFormsStmt = $pdo->prepare("
            SELECT
                COUNT(*) as total,
                SUM(CASE WHEN is_read = FALSE THEN 1 ELSE 0 END) as unread_count
            FROM contact_forms
            WHERE deleted = FALSE
        ");
        $contactFormsStmt->execute();
        $contactFormsStats = $contactFormsStmt->fetch();

        $contactMessagesStmt = $pdo->prepare("
            SELECT
                COUNT(*) as total,
                SUM(CASE WHEN is_read = FALSE THEN 1 ELSE 0 END) as unread_count
            FROM contact_messages
            WHERE deleted = FALSE
        ");
        $contactMessagesStmt->execute();
        $contactMessagesStats = $contactMessagesStmt->fetch();

        // Get callback stats
        $callbackStmt = $pdo->prepare("
            SELECT
                COUNT(*) as total,
                SUM(CASE WHEN is_read = FALSE THEN 1 ELSE 0 END) as unread_count
            FROM callback_requests
            WHERE deleted = FALSE
        ");
        $callbackStmt->execute();
        $callbackStats = $callbackStmt->fetch();

        // Get meeting stats
        $meetingStmt = $pdo->prepare("
            SELECT
                COUNT(*) as total,
                SUM(CASE WHEN is_read = FALSE THEN 1 ELSE 0 END) as unread_count,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_count,
                SUM(CASE WHEN status = 'confirmed' THEN 1 ELSE 0 END) as confirmed_count,
                SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_count
            FROM meeting_requests
            WHERE deleted = FALSE
        ");
        $meetingStmt->execute();
        $meetingStats = $meetingStmt->fetch();

        // Calculate totals
        $totalContacts = ($contactFormsStats['total'] ?? 0) + ($contactMessagesStats['total'] ?? 0);
        $unreadContacts = ($contactFormsStats['unread_count'] ?? 0) + ($contactMessagesStats['unread_count'] ?? 0);

        $stats = [
            'unread_contacts' => $unreadContacts,
            'unread_callbacks' => $callbackStats['unread_count'] ?? 0,
            'unread_meetings' => $meetingStats['unread_count'] ?? 0,
            'total_contacts' => $totalContacts,
            'total_meetings' => $meetingStats['total'] ?? 0,
            'total_callbacks' => $callbackStats['total'] ?? 0,
            'pending_meetings' => $meetingStats['pending_count'] ?? 0,
            'completed_meetings' => $meetingStats['completed_count'] ?? 0,
            'confirmed_meetings' => $meetingStats['confirmed_count'] ?? 0
        ];

        sendResponse(true, 'Dashboard stats retrieved successfully', $stats);

    } catch (PDOException $e) {
        error_log('Dashboard stats error: ' . $e->getMessage());
        sendResponse(false, 'İstatistikler alınırken hata oluştu');
    }

} else {
    http_response_code(405);
    sendResponse(false, 'Method not allowed');
}
