<?php
session_start();
require_once 'config.php';

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendResponse(false, 'Sadece POST istekleri kabul edilir.');
}

// Get client IP
$clientIP = getClientIP();

// Check rate limit - daha esnek limit
if (!rateLimitCheck($clientIP, 10, 300)) { // 10 requests per 5 minutes
    sendResponse(false, 'Çok fazla istek gönderdiniz. Lütfen daha sonra tekrar deneyin.');
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    sendResponse(false, 'Geçersiz JSON verisi.');
}

// Validate required fields - sadece temel alanlar zorunlu
$requiredFields = ['name', 'email', 'message'];
foreach ($requiredFields as $field) {
    if (empty($input[$field])) {
        sendResponse(false, 'Ad Soyad, E-posta ve Mesaj alanları zorunludur.');
    }
}

// Sanitize inputs
$name = sanitizeInput($input['name']);
$email = sanitizeInput($input['email']);
$phone = isset($input['phone']) ? sanitizeInput($input['phone']) : '';
$company = isset($input['company']) ? sanitizeInput($input['company']) : '';
$message = sanitizeInput($input['message']);

// Validate email
if (!validateEmail($email)) {
    sendResponse(false, 'Geçerli bir e-posta adresi giriniz.');
}

// Validate phone (only if provided)
if (!empty($phone) && !validatePhone($phone)) {
    sendResponse(false, 'Geçerli bir telefon numarası giriniz.');
}

// Validate lengths
if (strlen($name) < 2 || strlen($name) > 100) {
    sendResponse(false, 'İsim 2-100 karakter arasında olmalıdır.');
}

if (!empty($company) && (strlen($company) < 2 || strlen($company) > 100)) {
    sendResponse(false, 'Şirket adı 2-100 karakter arasında olmalıdır.');
}

if (strlen($message) < 10 || strlen($message) > 1000) {
    sendResponse(false, 'Mesaj 10-1000 karakter arasında olmalıdır.');
}

try {
    $pdo = getDBConnection();
    
    // Site ID'sini al (MGSAM için)
    $siteId = 4; // MGSAM site ID'si (veritabanından)

    // Insert contact message
    $stmt = $pdo->prepare("
        INSERT INTO contact_messages (site_code, full_name, email, phone, company, message, created_at, status, ip_address, user_agent, message_type)
        VALUES (?, ?, ?, ?, ?, ?, NOW(), 'new', ?, ?, 'contact')
    ");

    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';

    $stmt->execute([
        'mgsam',
        $name,
        $email,
        $phone,
        $company,
        $message,
        $clientIP,
        $userAgent
    ]);
    
    $messageId = $pdo->lastInsertId();
    
    // Send email notification (optional)
    $emailSent = sendEmailNotification([
        'id' => $messageId,
        'name' => $name,
        'email' => $email,
        'phone' => $phone,
        'company' => $company,
        'message' => $message
    ]);
    
    sendResponse(true, 'Mesajınız başarıyla gönderildi. En kısa sürede size dönüş yapacağız.', [
        'id' => $messageId,
        'email_sent' => $emailSent
    ]);
    
} catch (PDOException $e) {
    error_log('Contact form error: ' . $e->getMessage());
    sendResponse(false, 'Mesaj gönderilirken bir hata oluştu. Lütfen daha sonra tekrar deneyin.');
}

function sendEmailNotification($data) {
    // Email configuration - Bu kısmı kendi email ayarlarınıza göre düzenleyin
    $to = '<EMAIL>';
    $subject = 'Yeni İletişim Formu Mesajı - ' . $data['company'];
    
    $message = "
    Yeni bir iletişim formu mesajı alındı:
    
    ID: {$data['id']}
    İsim: {$data['name']}
    E-posta: {$data['email']}
    Telefon: {$data['phone']}
    Şirket: {$data['company']}
    
    Mesaj:
    {$data['message']}
    
    Tarih: " . date('d.m.Y H:i:s') . "
    ";
    
    $headers = [
        'From: <EMAIL>',
        'Reply-To: ' . $data['email'],
        'Content-Type: text/plain; charset=UTF-8'
    ];

    return mail($to, $subject, $message, implode("\r\n", $headers));
}
