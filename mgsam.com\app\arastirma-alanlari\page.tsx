'use client';

import Link from "next/link";
import Header from "../components/Header";
import Footer from "../components/Footer";

export default function ArastirmaAlanlari() {
  const arastirmaAlanlari = [
    {
      title: "Politika Analizi ve Etki Değerlendirmesi",
      description: "Mevcut kamu politikalarının et<PERSON>, hedeflere ulaşma derecesini ve toplumsal etkilerini bağımsız bir şekilde analiz ederek iyileştirme önerileri sunuyoruz.",
      icon: "📋",
      features: [
        "Politika etkinlik analizleri",
        "Toplumsal etki değerlendirmeleri",
        "İyileştirme önerileri",
        "Bağımsız değerlendirme raporları"
      ]
    },
    {
      title: "Senaryo ve Trend Analizleri",
      description: "Küresel eğilimleri ve riskleri dikkate alarak, farklı politika seçeneklerinin olası sonuçlarını değerlendiren gelecek odaklı senaryolar hazırlıyoruz.",
      icon: "🔮",
      features: [
        "Gelecek senaryoları",
        "Risk değerlendirmeleri",
        "Trend analizleri",
        "Politika seçenekleri"
      ]
    },
    {
      title: "Ulusal Güvenlik ve Savunma Sanayi",
      description: "Ülkelerin savunma ve uzay sanayii alanındaki stratejik vizyonlarını destekleyen, risk ve tehditleri değerlendiren özel raporlar hazırlıyoruz.",
      icon: "🛡️",
      features: [
        "Savunma sanayi analizleri",
        "Uzay teknolojileri araştırmaları",
        "Güvenlik risk değerlendirmeleri",
        "Stratejik tehdit analizleri"
      ]
    },
    {
      title: "Ekonomik Büyüme ve Sektörel Analizler",
      description: "Makroekonomik verileri değerlendirerek, sektörlerin rekabet gücünü artırmaya yönelik analizler ve raporlar sunuyoruz.",
      icon: "📈",
      features: [
        "Makroekonomik analizler",
        "Sektörel rekabet analizleri",
        "Büyüme stratejileri",
        "Ekonomik öngörüler"
      ]
    },
    {
      title: "Sosyal Politika Araştırmaları",
      description: "Toplumsal sorunları ve demografik değişimleri bilimsel metotlarla inceleyerek, kapsayıcı ve adil sosyal politikaların oluşturulmasına katkı sağlıyoruz.",
      icon: "👥",
      features: [
        "Demografik analizler",
        "Sosyal politika önerileri",
        "Toplumsal sorun analizleri",
        "Kapsayıcılık araştırmaları"
      ]
    },
    {
      title: "Kalkınma ve Bölgesel Planlama",
      description: "Bölgesel eşitsizlikleri gidermeye yönelik stratejik projeler geliştirmek ve sürdürülebilir kalkınma hedeflerine ulaşılmasına destek oluyoruz.",
      icon: "🌍",
      features: [
        "Bölgesel kalkınma planları",
        "Sürdürülebilir kalkınma",
        "Eşitsizlik analizleri",
        "Stratejik proje geliştirme"
      ]
    },
    {
      title: "Proje Tasarımı ve Yönetimi",
      description: "Kamu kurumlarının belirlediği hedeflere yönelik, ulusal ve uluslararası fon kaynaklarından yararlanabilecek projelerin tasarımını, yönetimini ve takibini yapıyoruz.",
      icon: "🎯",
      features: [
        "Proje tasarımı",
        "Fon kaynak yönetimi",
        "Proje takibi",
        "Performans değerlendirmesi"
      ]
    },
    {
      title: "Kurumsal Kapasite Geliştirme",
      description: "Kamu personelinin değişen ihtiyaçlara adaptasyonunu sağlamak için eğitim ve danışmanlık hizmetleri ile beyin gücüne katkı sağlıyoruz.",
      icon: "🎓",
      features: [
        "Personel eğitim programları",
        "Kurumsal danışmanlık",
        "Kapasite geliştirme",
        "Adaptasyon stratejileri"
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-white">
      <Header currentPage="arastirma-alanlari" />
      
      {/* Hero Section */}
      <section className="pt-32 pb-16 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700">
        <div className="container mx-auto px-6">
          <div className="text-center text-white">
            <div className="inline-flex items-center px-4 py-2 bg-white/10 rounded-full text-white text-sm font-medium mb-6 border border-white/20">
              <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
              Uzmanlık Alanlarımız
            </div>
            <h1 className="text-4xl md:text-6xl font-light mb-6 text-white">
              Araştırma Alanları
            </h1>
            <p className="text-xl text-white/90 max-w-3xl mx-auto">
              Kamu kurum ve kuruluşlarına yönelik kapsamlı araştırma ve analiz hizmetlerimiz ile stratejik karar alma süreçlerinize destek oluyoruz.
            </p>
          </div>
        </div>
      </section>

      {/* Araştırma Alanları */}
      <section className="py-20">
        <div className="container mx-auto px-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {arastirmaAlanlari.map((alan, index) => (
              <div key={index} className="bg-white rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-slate-100">
                <div className="flex items-start space-x-4">
                  <div className="text-4xl">{alan.icon}</div>
                  <div className="flex-1">
                    <h3 className="text-2xl font-semibold text-slate-900 mb-4">{alan.title}</h3>
                    <p className="text-slate-600 mb-6 leading-relaxed">{alan.description}</p>
                    
                    <div className="space-y-2">
                      <h4 className="text-sm font-semibold text-slate-900 uppercase tracking-wide">Hizmet Kapsamı:</h4>
                      <ul className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                        {alan.features.map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-center text-sm text-slate-600">
                            <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"></span>
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Metodoloji */}
      <section className="py-20 bg-slate-50">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-light text-slate-900 mb-4">
              Araştırma Metodolojimiz
            </h2>
            <p className="text-lg text-slate-600 max-w-2xl mx-auto">
              Bilimsel ve tarafsız yaklaşımımızla güvenilir sonuçlar üretiyoruz
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                step: "01",
                title: "Veri Toplama ve Analiz",
                description: "Birincil ve ikincil kaynaklardan sistematik veri toplama ve bilimsel analiz yöntemleri kullanıyoruz."
              },
              {
                step: "02",
                title: "Uzman Görüşleri",
                description: "Alanında uzman akademisyen ve pratisyenlerden görüş alarak çok boyutlu perspektif sağlıyoruz."
              },
              {
                step: "03",
                title: "Raporlama ve Sunum",
                description: "Bulgularımızı anlaşılır ve uygulanabilir öneriler halinde karar alıcılara sunuyoruz."
              }
            ].map((item, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-slate-600 rounded-full mx-auto mb-6 flex items-center justify-center">
                  <span className="text-white font-bold text-lg">{item.step}</span>
                </div>
                <h3 className="text-xl font-semibold text-slate-900 mb-4">{item.title}</h3>
                <p className="text-slate-600 leading-relaxed">{item.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700">
        <div className="container mx-auto px-6 text-center">
          <h2 className="text-3xl md:text-4xl font-light text-white mb-6">
            Araştırma Projesi Başlatalım
          </h2>
          <p className="text-xl text-white/90 max-w-2xl mx-auto mb-8">
            Kurumunuzun ihtiyaçlarına özel araştırma ve analiz hizmetlerimiz hakkında detaylı bilgi alın.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/iletisim"
              className="inline-flex items-center px-8 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-300"
            >
              Proje Teklifi Alın
            </Link>
            <Link
              href="/yayinlar"
              className="inline-flex items-center px-8 py-3 bg-white/10 hover:bg-white/20 text-white font-medium rounded-lg border border-white/20 transition-colors duration-300"
            >
              Örnek Çalışmalarımız
            </Link>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
