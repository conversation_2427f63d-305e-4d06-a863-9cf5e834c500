'use client';

import { useEffect } from 'react';

declare global {
  namespace JSX {
    interface IntrinsicElements {
      'elevenlabs-convai': React.DetailedHTMLProps<
        React.HTMLAttributes<HTMLElement> & {
          'agent-id': string;
        },
        HTMLElement
      >;
    }
  }
}

export default function ElevenLabsAgent() {
  useEffect(() => {
    // Check if script is already loaded
    const existingScript = document.querySelector('script[src="https://unpkg.com/@elevenlabs/convai-widget-embed"]');

    if (!existingScript) {
      const script = document.createElement('script');
      script.src = 'https://unpkg.com/@elevenlabs/convai-widget-embed';
      script.async = true;
      script.type = 'text/javascript';
      script.id = 'elevenlabs-widget-script';

      // Add error handling
      script.onerror = () => {
        console.warn('ElevenLabs widget failed to load');
      };

      document.body.appendChild(script);
    }

    return () => {
      // Don't remove script on cleanup to prevent re-registration issues
      // The script should only be loaded once per page
    };
  }, []);

  return (
    <elevenlabs-convai
      agent-id="agent_2601k1bb7pkee5sbxpfezxpgedwh"
      className="w-full h-full"
    />
  );
}
