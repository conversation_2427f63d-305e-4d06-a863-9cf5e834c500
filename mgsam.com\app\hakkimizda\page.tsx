'use client';

import Link from "next/link";
import Header from "../components/Header";
import Footer from "../components/Footer";

export default function Hakkimizda() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-white">
      <Header currentPage="hakkimizda" />
      
      {/* Hero Section */}
      <section className="pt-32 pb-16 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700">
        <div className="container mx-auto px-6">
          <div className="text-center text-white">
            <div className="inline-flex items-center px-4 py-2 bg-white/10 rounded-full text-white text-sm font-medium mb-6 border border-white/20">
              <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
              Bağımsız Düşünce Kuruluşu
            </div>
            <h1 className="text-4xl md:text-6xl font-light mb-6 text-white">
              Hakkımızda
            </h1>
            <p className="text-xl text-white/90 max-w-3xl mx-auto">
              Meta Global Stratejiler Ekonomik ve Siyasal Araştırmalar Merkezi olarak, kamu kurum ve kuruluşlarının karar alma süreçlerine bilimsel bilgi ve stratejik analizlerle destek oluyoruz.
            </p>
          </div>
        </div>
      </section>

      {/* Misyon ve Vizyon */}
      <section className="py-20">
        <div className="container mx-auto px-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-light text-slate-900 mb-6">
                Misyonumuz
              </h2>
              <p className="text-lg text-slate-600 mb-6 leading-relaxed">
                Meta Analiz Group Müşavirlik Danışmanlık Limited Şirketi bünyesinde faaliyet gösteren merkezimiz, 
                bağımsız bir düşünce kuruluşu (think tank) olarak, evrensel ve ulusal gelişmeleri takip ederek, 
                ortaya çıkan problemlere yönelik gerçekçi, dinamik ve etkin çözümler üretmektir.
              </p>
              <p className="text-lg text-slate-600 leading-relaxed">
                Temel amacımız, kamu kurum ve kuruluşlarının <strong>"doğru bilgi"</strong>ye erişimini sağlayarak 
                <strong>"iyi yönetim"</strong> ve <strong>"sürdürülebilir kalkınma"</strong> hedeflerine ulaşmasında 
                kritik bir rol oynamaktır.
              </p>
            </div>
            <div className="bg-gradient-to-br from-blue-50 to-slate-50 p-8 rounded-2xl">
              <h3 className="text-2xl font-semibold text-slate-900 mb-4">Temel Değerlerimiz</h3>
              <ul className="space-y-3">
                <li className="flex items-center text-slate-700">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                  Bilimsel ve tarafsız yaklaşım
                </li>
                <li className="flex items-center text-slate-700">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                  Yüksek etik değerler
                </li>
                <li className="flex items-center text-slate-700">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                  Şeffaflık ilkesi
                </li>
                <li className="flex items-center text-slate-700">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                  Ulusal ve uluslararası işbirliği
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Hizmet Alanları */}
      <section className="py-20 bg-slate-50">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-light text-slate-900 mb-4">
              Ana Hizmet Alanlarımız
            </h2>
            <p className="text-lg text-slate-600 max-w-2xl mx-auto">
              Kamu kurum ve kuruluşlarına yönelik kapsamlı hizmet portföyümüz
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                title: "Stratejik Analiz ve Politika Geliştirme",
                description: "Ulusal ve uluslararası gelişmeleri analiz ederek geleceğe yönelik stratejik öngörüler sunuyoruz.",
                icon: "📊"
              },
              {
                title: "Ekonomik ve Sosyal Araştırmalar",
                description: "Ekonomik kalkınma ve sosyal refah hedeflerini destekleyen bilimsel araştırmalar yürütüyoruz.",
                icon: "📈"
              },
              {
                title: "Proje Yönetimi ve Danışmanlık",
                description: "Stratejik projelerin tasarımı, yönetimi ve değerlendirilmesi konularında uzmanlık sunuyoruz.",
                icon: "🎯"
              },
              {
                title: "Ulusal Güvenlik ve Savunma Sanayi",
                description: "Savunma ve uzay sanayii alanında stratejik vizyon destekleyen özel raporlar hazırlıyoruz.",
                icon: "🛡️"
              },
              {
                title: "Yayınlar ve Bilgi Üretimi",
                description: "Politika notları, raporlar ve akademik yayınlarla bilgi üretimini teşvik ediyoruz.",
                icon: "📚"
              },
              {
                title: "Uluslararası İşbirliği",
                description: "Ortak projeler geliştirmek ve uluslararası işbirliklerine katılım için danışmanlık sunuyoruz.",
                icon: "🌍"
              }
            ].map((service, index) => (
              <div key={index} className="bg-white p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
                <div className="text-4xl mb-4">{service.icon}</div>
                <h3 className="text-xl font-semibold text-slate-900 mb-3">{service.title}</h3>
                <p className="text-slate-600 leading-relaxed">{service.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Liderlik */}
      <section className="py-20">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-light text-slate-900 mb-4">
              Liderlik
            </h2>
            <p className="text-lg text-slate-600 max-w-2xl mx-auto">
              Deneyimli kadromuzla stratejik vizyonumuzu hayata geçiriyoruz
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <div className="bg-gradient-to-br from-slate-50 to-blue-50 p-8 md:p-12 rounded-3xl">
              <div className="text-center">
                <div className="w-24 h-24 bg-gradient-to-br from-blue-600 to-slate-600 rounded-full mx-auto mb-6 flex items-center justify-center">
                  <span className="text-white text-2xl font-bold">ÖA</span>
                </div>
                <h3 className="text-2xl md:text-3xl font-semibold text-slate-900 mb-2">
                  Dr. (Ph.D.) Özgür Atacan
                </h3>
                <p className="text-lg text-blue-600 mb-6">Genel Müdür</p>
                <p className="text-slate-600 leading-relaxed max-w-2xl mx-auto">
                  Meta Analiz Group Müşavirlik Danışmanlık Limited Şirketi bünyesinde, 
                  ulusal ve uluslararası düzeyde güçlü bir paydaş olma vizyonuyla 
                  merkezimizin çalışmalarını yönetmektedir.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* İletişim CTA */}
      <section className="py-20 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700">
        <div className="container mx-auto px-6 text-center">
          <h2 className="text-3xl md:text-4xl font-light text-white mb-6">
            Birlikte Çalışalım
          </h2>
          <p className="text-xl text-white/90 max-w-2xl mx-auto mb-8">
            Kurumunuzun stratejik hedeflerine ulaşması için bilimsel ve tarafsız analizlerimizle yanınızdayız.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/iletisim"
              className="inline-flex items-center px-8 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-300"
            >
              İletişime Geçin
            </Link>
            <Link
              href="/projeler"
              className="inline-flex items-center px-8 py-3 bg-white/10 hover:bg-white/20 text-white font-medium rounded-lg border border-white/20 transition-colors duration-300"
            >
              Projelerimizi İnceleyin
            </Link>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
