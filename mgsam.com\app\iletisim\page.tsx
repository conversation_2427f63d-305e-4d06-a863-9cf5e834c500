'use client';

import React, { useState } from 'react';
import Header from "../components/Header";
import Footer from "../components/Footer";

export default function Iletisim() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    company: '',
    message: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState('');
  const [submitSuccess, setSubmitSuccess] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitMessage('');

    try {
      const response = await fetch('/api/contact.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (result.success) {
        setSubmitSuccess(true);
        setSubmitMessage(result.message);
        setFormData({
          name: '',
          email: '',
          phone: '',
          company: '',
          message: ''
        });
      } else {
        setSubmitSuccess(false);
        setSubmitMessage(result.message);
      }
    } catch (error) {
      console.error('Form submission error:', error);
      setSubmitSuccess(false);
      setSubmitMessage('Bir hata oluştu. Lütfen daha sonra tekrar deneyin.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-white flex flex-col">
      <Header currentPage="iletisim" />

      {/* Main Content */}
      <main className="flex-1 pt-20 pb-8">
        <div className="container mx-auto px-4">
          <div className="max-w-7xl mx-auto flex flex-col">

            {/* Compact Header - Hidden on mobile */}
            <div className="text-center py-4 hidden md:block">
              <div className="inline-flex items-center px-3 py-1 bg-slate-100 rounded-full text-slate-700 text-sm font-medium mb-3">
                <span className="w-2 h-2 bg-gradient-to-r from-green-500 to-green-400 rounded-full mr-2"></span>
                {' '}Bizimle İletişime Geçin
              </div>
              <h1 className="text-3xl md:text-4xl font-extralight text-slate-900 mb-2 tracking-tight">
                Profesyonel{' '}
                <span className="text-slate-700 font-light">Danışmanlık</span>
              </h1>
              <p className="text-slate-600 font-light text-sm">
                Kurumsal çözümleriniz için uzman ekibimizle görüşün
              </p>
            </div>

            {/* Main Content Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 py-8">

              {/* İletişim Formu - Mobile First */}
              <div className="bg-white rounded-3xl p-6 shadow-lg border border-slate-200/50 relative overflow-hidden lg:order-2">
                <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-slate-100/30 to-transparent rounded-full -translate-y-16 translate-x-16"></div>

                <div className="relative z-10 h-full flex flex-col">
                  <div className="mb-4">
                    <h2 className="text-xl font-light text-slate-900 mb-1 tracking-tight">
                      Mesaj Gönder
                    </h2>
                    <p className="text-slate-600 text-sm font-light">
                      24 saat içinde profesyonel yanıt alın
                    </p>
                  </div>
                  
                  <form onSubmit={handleSubmit} className="flex-1 flex flex-col space-y-3">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      <div>
                        <input
                          type="text"
                          name="name"
                          placeholder="Adınız Soyadınız *"
                          value={formData.name}
                          onChange={handleChange}
                          required
                          className="w-full px-4 py-2.5 border border-slate-200 rounded-xl focus:ring-2 focus:ring-slate-400 focus:border-transparent transition-all duration-200 text-sm font-light"
                        />
                      </div>
                      <div>
                        <input
                          type="email"
                          name="email"
                          placeholder="E-posta Adresiniz *"
                          value={formData.email}
                          onChange={handleChange}
                          required
                          className="w-full px-4 py-2.5 border border-slate-200 rounded-xl focus:ring-2 focus:ring-slate-400 focus:border-transparent transition-all duration-200 text-sm font-light"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      <div>
                        <input
                          type="tel"
                          name="phone"
                          placeholder="Telefon Numaranız"
                          value={formData.phone}
                          onChange={handleChange}
                          className="w-full px-4 py-2.5 border border-slate-200 rounded-xl focus:ring-2 focus:ring-slate-400 focus:border-transparent transition-all duration-200 text-sm font-light"
                        />
                      </div>
                      <div>
                        <input
                          type="text"
                          name="company"
                          placeholder="Şirket Adınız *"
                          value={formData.company}
                          onChange={handleChange}
                          required
                          className="w-full px-4 py-2.5 border border-slate-200 rounded-xl focus:ring-2 focus:ring-slate-400 focus:border-transparent transition-all duration-200 text-sm font-light"
                        />
                      </div>
                    </div>

                    <div>
                      <textarea
                        name="message"
                        placeholder="Mesajınız *"
                        value={formData.message}
                        onChange={handleChange}
                        required
                        rows={4}
                        className="w-full px-4 py-2.5 border border-slate-200 rounded-xl focus:ring-2 focus:ring-slate-400 focus:border-transparent transition-all duration-200 text-sm font-light resize-none"
                      ></textarea>
                    </div>

                    {/* Submit Message */}
                    {submitMessage && (
                      <div className={`p-3 rounded-xl text-sm font-medium ${
                        submitSuccess
                          ? 'bg-green-50 text-green-700 border border-green-200'
                          : 'bg-red-50 text-red-700 border border-red-200'
                      }`}>
                        {submitMessage}
                      </div>
                    )}

                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className={`w-full py-2.5 px-6 rounded-xl font-medium transition-all duration-300 transform shadow-lg text-sm ${
                        isSubmitting
                          ? 'bg-slate-400 text-white cursor-not-allowed'
                          : 'bg-gradient-to-r from-slate-900 to-slate-700 text-white hover:from-slate-800 hover:to-slate-600 hover:-translate-y-1 hover:shadow-xl'
                      }`}
                    >
                      {isSubmitting ? 'Gönderiliyor...' : 'Mesajı Gönder'}
                    </button>
                  </form>


                </div>
              </div>

              {/* İletişim Bilgileri */}
              <div className="bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700 rounded-3xl p-6 text-white relative overflow-hidden lg:order-1">
                <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width=%2240%22 height=%2240%22 viewBox=%220 0 40 40%22 xmlns=%22http://www.w3.org/2000/svg%22%3E%3Cg fill=%22none%22 fill-rule=%22evenodd%22%3E%3Cg fill=%22%23ffffff%22 fill-opacity=%220.03%22%3E%3Cpath d=%22m0 40l40-40h-40z%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-30"></div>

                <div className="relative z-10 h-full flex flex-col">
                  <div className="mb-4">
                    <h2 className="text-xl font-light text-white mb-1 tracking-tight">
                      İletişim Bilgileri
                    </h2>
                    <p className="text-slate-300 text-sm font-light">
                      Hızlı ve profesyonel yanıt garantisi
                    </p>
                  </div>
                  
                  <div className="flex-1 space-y-4">
                    {/* Adres */}
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-slate-700/50 backdrop-blur-sm rounded-2xl flex items-center justify-center flex-shrink-0 border border-slate-600/30">
                        <svg className="w-5 h-5 text-slate-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                      </div>
                      <div>
                        <h3 className="font-medium text-white mb-1 text-sm">Merkez Ofis</h3>
                        <p className="text-slate-300 text-sm font-light">
                          Yenicami Mah. Özmen Sok. No: 24/A<br />
                          Söke / Aydın
                        </p>
                      </div>
                    </div>

                    {/* Telefon */}
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-slate-700/50 backdrop-blur-sm rounded-2xl flex items-center justify-center flex-shrink-0 border border-slate-600/30">
                        <svg className="w-5 h-5 text-slate-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                        </svg>
                      </div>
                      <div>
                        <h3 className="font-medium text-white mb-1 text-sm">Direkt Hat</h3>
                        <p className="text-slate-300 text-sm font-light">+90 (542) 380 00 50</p>
                      </div>
                    </div>

                    {/* E-posta */}
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-slate-700/50 backdrop-blur-sm rounded-2xl flex items-center justify-center flex-shrink-0 border border-slate-600/30">
                        <svg className="w-5 h-5 text-slate-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                      </div>
                      <div>
                        <h3 className="font-medium text-white mb-1 text-sm">E-posta</h3>
                        <p className="text-slate-300 text-sm font-light"><EMAIL></p>
                        <p className="text-slate-300 text-sm font-light"><EMAIL></p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>


          </div>
        </div>
      </main>

      {/* Google Maps - Tam Genişlik */}
      <section className="w-full h-80">
        <iframe
          src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3141.8947!2d27.3236!3d37.7507!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x14bfd3c5e5e5e5e5%3A0x5e5e5e5e5e5e5e5e!2sYenicami%20Mah.%20%C3%96zmen%20Sok.%20No%3A24%2FA%2C%2009270%20S%C3%B6ke%2FAyd%C4%B1n!5e0!3m2!1str!2str!4v1640995200000!5m2!1str!2str"
          width="100%"
          height="100%"
          style={{ border: 0 }}
          allowFullScreen
          loading="lazy"
          referrerPolicy="no-referrer-when-downgrade"
          title="Meta Analiz Group Ofis Konumu - Yenicami Mah. Özmen Sok. No: 24/A Söke, Aydın"
        ></iframe>
      </section>

      <Footer />
    </div>
  );
}