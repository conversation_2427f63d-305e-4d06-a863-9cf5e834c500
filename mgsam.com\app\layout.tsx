import type { <PERSON><PERSON><PERSON>, Viewport } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-inter",
});

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
};

export const metadata: Metadata = {
  metadataBase: new URL("https://mgsam.com"),
  title: "MGSAM - Meta Global Stratejiler Ekonomik ve Siyasal Araştırmalar Merkezi",
  description: "Bağımsız düşünce kuruluşu olarak kamu kurum ve kuruluşlarının karar alma süreçlerine bilimsel bilgi ve stratejik analizlerle destek oluyoruz. Doğru bilgi, iyi yönetim, sürdürülebilir kalkınma.",
  keywords: "think tank, düşünce kuruluşu, ekonomik araştırmalar, siyasal araştırmalar, politika geliştirme, stratejik analiz, ka<PERSON> politi<PERSON>, jeo<PERSON><PERSON><PERSON>, savu<PERSON><PERSON>, ul<PERSON>, MGSAM",
  authors: [{ name: "Dr. (Ph.D.) Özgür Atacan", url: "https://mgsam.com" }],
  robots: "index, follow",
  alternates: {
    canonical: "https://mgsam.com",
  },
  verification: {
    google: "google-site-verification-code", // Google Search Console'dan alınacak
  },
  icons: {
    icon: [
      { url: "/mgsam.webp", sizes: "32x32", type: "image/webp" },
      { url: "/mgsam.webp", sizes: "16x16", type: "image/webp" }
    ],
    apple: [
      { url: "/mgsam.webp", sizes: "180x180", type: "image/webp" }
    ],
    shortcut: "/mgsam.webp"
  },
  openGraph: {
    title: "MGSAM - Meta Global Stratejiler Ekonomik ve Siyasal Araştırmalar Merkezi",
    description: "Türkiye'den tüm dünyaya ekonomik ve siyasal araştırmalar merkezi. Yaşamın Kalitesi, Yönetimin Kalitesi ile Artar.",
    url: "https://mgsam.com",
    siteName: "MGSAM",
    images: [
      {
        url: "/mgsam_logo.webp",
        width: 1200,
        height: 630,
        alt: "MGSAM Logo"
      }
    ],
    locale: "tr_TR",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "MGSAM - Meta Global Stratejiler Ekonomik ve Siyasal Araştırmalar Merkezi",
    description: "Türkiye'den tüm dünyaya ekonomik ve siyasal araştırmalar merkezi. Yaşamın Kalitesi, Yönetimin Kalitesi ile Artar.",
    images: ["/mgsam_logo.webp"],
  },
  other: {
    'theme-color': '#1e293b',
    'color-scheme': 'light',
    'format-detection': 'telephone=no',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="tr" className={inter.variable}>
      <head>
      </head>
      <body className="font-inter antialiased">
        {children}
      </body>
    </html>
  );
}