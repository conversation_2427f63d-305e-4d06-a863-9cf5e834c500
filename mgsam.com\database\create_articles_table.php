<?php
// Articles tablosu oluşturma scripti
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Config dosyasını include et
require_once __DIR__ . '/../api/config.php';

echo "<h2>🚀 Articles Tablosu Oluşturma</h2>";

try {
    $pdo = getDBConnection();
    echo "<p style='color: green;'>✅ Veritabanı bağlantısı başarılı!</p>";
    
    // SQL dosyasını oku
    $sqlFile = __DIR__ . '/articles_table.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL dosyası bulunamadı: $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    echo "<p>📄 SQL dosyası okundu: " . strlen($sql) . " karakter</p>";
    
    // SQL komutlarını ayır ve çalıştır
    $statements = explode(';', $sql);
    $successCount = 0;
    $errorCount = 0;
    
    echo "<h3>📋 SQL Komutları Çalıştırılıyor:</h3>";
    echo "<div style='background: #f5f5f5; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        
        // Boş satırları ve yorumları atla
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        try {
            $pdo->exec($statement);
            
            // Komut türünü belirle
            $commandType = 'UNKNOWN';
            if (stripos($statement, 'CREATE TABLE') !== false) {
                preg_match('/CREATE TABLE.*?`?(\w+)`?/i', $statement, $matches);
                $commandType = 'CREATE TABLE: ' . ($matches[1] ?? 'unknown');
            } elseif (stripos($statement, 'INSERT') !== false) {
                preg_match('/INSERT.*?INTO\s+`?(\w+)`?/i', $statement, $matches);
                $commandType = 'INSERT INTO: ' . ($matches[1] ?? 'unknown');
            } elseif (stripos($statement, 'ALTER TABLE') !== false) {
                preg_match('/ALTER TABLE\s+`?(\w+)`?/i', $statement, $matches);
                $commandType = 'ALTER TABLE: ' . ($matches[1] ?? 'unknown');
            }
            
            echo "<p style='color: green;'>✅ $commandType</p>";
            $successCount++;
            
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ Hata: " . $e->getMessage() . "</p>";
            echo "<p style='color: gray; font-size: 12px;'>SQL: " . substr($statement, 0, 100) . "...</p>";
            $errorCount++;
        }
    }
    
    echo "</div>";
    
    echo "<h3>📊 Sonuç:</h3>";
    echo "<p><strong>Başarılı:</strong> $successCount komut</p>";
    echo "<p><strong>Hatalı:</strong> $errorCount komut</p>";
    
    // Articles tablosunu kontrol et
    echo "<h3>🔍 Articles Tablosu Kontrolü:</h3>";
    try {
        $stmt = $pdo->query("DESCRIBE articles");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>Sütun</th><th>Tür</th><th>Null</th><th>Anahtar</th><th>Varsayılan</th>";
        echo "</tr>";
        
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td><strong>" . $column['Field'] . "</strong></td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Makale sayısını kontrol et
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM articles");
        $count = $stmt->fetch()['count'];
        echo "<p><strong>Toplam makale sayısı:</strong> $count</p>";
        
        if ($count > 0) {
            echo "<h4>📰 Mevcut Makaleler:</h4>";
            $stmt = $pdo->query("SELECT id, title, slug, status, published_at FROM articles ORDER BY published_at DESC");
            $articles = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<ul>";
            foreach ($articles as $article) {
                $status_color = $article['status'] === 'published' ? 'green' : 'orange';
                echo "<li>";
                echo "<strong>" . htmlspecialchars($article['title']) . "</strong> ";
                echo "<span style='color: $status_color;'>(" . $article['status'] . ")</span>";
                echo "<br><small>Slug: " . $article['slug'] . " | Tarih: " . $article['published_at'] . "</small>";
                echo "</li>";
            }
            echo "</ul>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ Articles tablosu kontrol edilemedi: " . $e->getMessage() . "</p>";
    }
    
    echo "<h3>🎯 Sonuç:</h3>";
    if ($errorCount === 0) {
        echo "<p style='color: green; font-size: 18px; font-weight: bold;'>🎉 Articles tablosu başarıyla oluşturuldu!</p>";
        echo "<p>✅ Makale yönetim sistemi kullanıma hazır.</p>";
        echo "<p>✅ Admin panelinden makale ekleyebilirsiniz.</p>";
    } else {
        echo "<p style='color: orange; font-size: 18px; font-weight: bold;'>⚠️ Bazı hatalar oluştu, ancak tablo oluşturulmuş olabilir.</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red; font-size: 18px; font-weight: bold;'>❌ Kritik Hata: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='../control-area/dashboard/articles'>👉 Admin Paneline Git</a></p>";
echo "<p><small>Bu sayfayı kapatabilirsiniz.</small></p>";
?>
