import mysql from 'mysql2/promise';
import bcrypt from 'bcryptjs';

// Database connection configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'metaanalizgroup_gr',
  password: process.env.DB_PASSWORD || 'Mah2025!',
  database: process.env.DB_NAME || 'metaanalizgroup_meta',
  charset: process.env.DB_CHARSET || 'utf8mb4',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
};

// Create connection pool
const pool = mysql.createPool(dbConfig);

// Database connection function
export async function connectDB() {
  try {
    const connection = await pool.getConnection();
    console.log('Database connected successfully');
    return connection;
  } catch (error) {
    console.error('Database connection failed:', error);
    throw error;
  }
}

// Execute query function
export async function executeQuery(query: string, params: any[] = []) {
  try {
    const connection = await pool.getConnection();
    const [results] = await connection.execute(query, params);
    connection.release();
    return results;
  } catch (error) {
    console.error('Query execution failed:', error);
    throw error;
  }
}

// Contact form submission - mevcut contact_messages tablosuna uygun
export async function saveContactForm(data: {
  name: string;
  email: string;
  phone?: string;
  company?: string;
  message: string;
}) {
  const query = `
    INSERT INTO contact_messages (site_id, source, name, email, phone, company, message, created_at, status, ip_address, user_agent)
    VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), 'new', ?, ?)
  `;

  const params = [
    2, // MGSAM site_id
    'mgsam', // source
    data.name,
    data.email,
    data.phone || null,
    data.company || null,
    data.message,
    null, // ip_address - client'tan alınabilir
    null  // user_agent - client'tan alınabilir
  ];

  try {
    const result = await executeQuery(query, params);
    return result;
  } catch (error) {
    console.error('Failed to save contact form:', error);
    throw error;
  }
}

// Meeting form submission - mevcut meeting_requests tablosuna uygun
export async function saveMeetingForm(data: {
  name: string;
  email: string;
  phone?: string;
  company?: string;
  service: string;
  meeting_date: string;
  meeting_time: string;
  message?: string;
}) {
  const query = `
    INSERT INTO meeting_requests (site_id, source, name, email, phone, company, service, meeting_date, meeting_time, message, created_at, status, ip_address, user_agent, notes)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), 'pending', ?, ?, ?)
  `;

  const params = [
    2, // MGSAM site_id
    'mgsam', // source
    data.name,
    data.email,
    data.phone || null,
    data.company || null,
    data.service,
    data.meeting_date,
    data.meeting_time,
    data.message || null,
    null, // ip_address - client'tan alınabilir
    null, // user_agent - client'tan alınabilir
    null  // notes - admin tarafından doldurulur
  ];

  try {
    const result = await executeQuery(query, params);
    return result;
  } catch (error) {
    console.error('Failed to save meeting request:', error);
    throw error;
  }
}

// Admin user functions
export async function createAdminUser(username: string, email: string, password: string, fullName: string) {
  const hashedPassword = await bcrypt.hash(password, 12);

  const query = `
    INSERT INTO admin_users (username, email, password_hash, full_name, role, is_active, created_at)
    VALUES (?, ?, ?, ?, 'admin', 1, NOW())
  `;

  const params = [username, email, hashedPassword, fullName];

  try {
    const result = await executeQuery(query, params);
    return result;
  } catch (error) {
    console.error('Failed to create admin user:', error);
    throw error;
  }
}

export async function verifyAdminUser(username: string, password: string) {

  const query = `
    SELECT id, username, email, password_hash, full_name, role, is_active
    FROM admin_users
    WHERE username = ? AND is_active = 1
  `;

  try {
    const results: any = await executeQuery(query, [username]);

    if (Array.isArray(results) && results.length > 0) {
      const user = results[0];
      const isValidPassword = await bcrypt.compare(password, user.password_hash);

      if (isValidPassword) {
        // Update last login
        await executeQuery(
          'UPDATE admin_users SET last_login = NOW() WHERE id = ?',
          [user.id]
        );

        return {
          id: user.id,
          username: user.username,
          email: user.email,
          fullName: user.full_name,
          role: user.role
        };
      }
    }

    return null;
  } catch (error) {
    console.error('Failed to verify admin user:', error);
    throw error;
  }
}

// Get contact messages for admin
export async function getContactMessages(page = 1, limit = 10, status?: string) {
  let query = `
    SELECT id, name, email, phone, company, message, created_at, status
    FROM contact_messages
  `;

  const params: any[] = [];

  if (status) {
    query += ' WHERE status = ?';
    params.push(status);
  }

  query += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
  params.push(limit, (page - 1) * limit);

  try {
    const results = await executeQuery(query, params);
    return results;
  } catch (error) {
    console.error('Failed to get contact messages:', error);
    throw error;
  }
}

// Get meeting requests for admin
export async function getMeetingRequests(page = 1, limit = 10, status?: string) {
  let query = `
    SELECT id, name, email, phone, company, service, meeting_date, meeting_time, message, created_at, status
    FROM meeting_requests
  `;

  const params: any[] = [];

  if (status) {
    query += ' WHERE status = ?';
    params.push(status);
  }

  query += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
  params.push(limit, (page - 1) * limit);

  try {
    const results = await executeQuery(query, params);
    return results;
  } catch (error) {
    console.error('Failed to get meeting requests:', error);
    throw error;
  }
}

// Update message status
export async function updateContactMessageStatus(id: number, status: string) {
  const query = 'UPDATE contact_messages SET status = ? WHERE id = ?';

  try {
    const result = await executeQuery(query, [status, id]);
    return result;
  } catch (error) {
    console.error('Failed to update contact message status:', error);
    throw error;
  }
}

// Update meeting request status
export async function updateMeetingRequestStatus(id: number, status: string) {
  const query = 'UPDATE meeting_requests SET status = ? WHERE id = ?';

  try {
    const result = await executeQuery(query, [status, id]);
    return result;
  } catch (error) {
    console.error('Failed to update meeting request status:', error);
    throw error;
  }
}

// Delete contact message
export async function deleteContactMessage(id: number) {
  const query = 'DELETE FROM contact_messages WHERE id = ?';

  try {
    const result = await executeQuery(query, [id]);
    return result;
  } catch (error) {
    console.error('Failed to delete contact message:', error);
    throw error;
  }
}

// Delete meeting request
export async function deleteMeetingRequest(id: number) {
  const query = 'DELETE FROM meeting_requests WHERE id = ?';

  try {
    const result = await executeQuery(query, [id]);
    return result;
  } catch (error) {
    console.error('Failed to delete meeting request:', error);
    throw error;
  }
}

export default pool;
