(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[745],{206:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var a=s(5155),l=s(7864),r=s(5922),i=s(2115);let n=e=>{if(!e)return"";let t=e;return!(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=t.replace(/<b\b[^>]*>(.*?)<\/b>/gi,"<strong>$1</strong>")).replace(/<strong\b[^>]*>(.*?)<\/strong>/gi,"<strong>$1</strong>")).replace(/<i\b[^>]*>(.*?)<\/i>/gi,"<em>$1</em>")).replace(/<em\b[^>]*>(.*?)<\/em>/gi,"<em>$1</em>")).replace(/<p\b[^>]*>/gi,'<p class="mb-4">')).replace(/<h1\b[^>]*>(.*?)<\/h1>/gi,'<h1 class="text-3xl font-bold mt-8 mb-6 text-gray-900">$1</h1>')).replace(/<h2\b[^>]*>(.*?)<\/h2>/gi,'<h2 class="text-2xl font-semibold mt-8 mb-6 text-gray-900">$1</h2>')).replace(/<h3\b[^>]*>(.*?)<\/h3>/gi,'<h3 class="text-xl font-semibold mt-6 mb-4 text-gray-900">$1</h3>')).replace(/<h4\b[^>]*>(.*?)<\/h4>/gi,'<h4 class="text-lg font-semibold mt-4 mb-2 text-gray-900">$1</h4>')).replace(/<table\b[^>]*>/gi,'<div class="overflow-x-auto mb-6"><table class="min-w-full border border-gray-300 bg-white rounded-lg shadow-sm">')).replace(/<\/table>/gi,"</table></div>")).replace(/<th\b[^>]*>/gi,'<th class="border border-gray-300 px-4 py-3 bg-gray-50 text-left font-semibold text-gray-900">')).replace(/<td\b[^>]*>/gi,'<td class="border border-gray-300 px-4 py-3 text-gray-800">')).replace(/style="[^"]*"/gi,"")).replace(/<p(?![^>]*class=)>/gi,'<p class="mb-4">')).replace(/<h1(?![^>]*class=)>/gi,'<h1 class="text-3xl font-bold mt-8 mb-6 text-gray-900">')).replace(/<h2(?![^>]*class=)>/gi,'<h2 class="text-2xl font-semibold mt-8 mb-6 text-gray-900">')).replace(/<h3(?![^>]*class=)>/gi,'<h3 class="text-xl font-semibold mt-6 mb-4 text-gray-900">')).replace(/<h4(?![^>]*class=)>/gi,'<h4 class="text-lg font-semibold mt-4 mb-2 text-gray-900">')).includes("<")&&((t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=t.replace(/^(MEGSAM Analiz Raporu)$/gm,'<h1 class="text-3xl font-bold mt-8 mb-6 text-gray-900">$1</h1>')).replace(/^(Hazırlayan|Tarih):\s*(.+)$/gm,'<p class="mb-4"><strong>$1:</strong> $2</p>')).replace(/^(Yönetici Özeti|Giriş|Sonuç)$/gm,'<h2 class="text-2xl font-semibold mt-8 mb-6 text-gray-900">$1</h2>')).replace(/^(\d+\))\s*(.+)$/gm,'<h2 class="text-2xl font-semibold mt-8 mb-6 text-gray-900">$1 $2</h2>')).replace(/^(\d+\.\d+\))\s*(.+)$/gm,'<h3 class="text-xl font-semibold mt-6 mb-4 text-gray-900">$1 $2</h3>')).replace(/^(Ne\?|Ne zaman & nerede\?|Kim\?|Nasıl\?|Neden\?|Analiz:)$/gm,'<h4 class="text-lg font-semibold mt-4 mb-2 text-gray-900">$1</h4>')).replace(/\*\*(.+?)\*\*/g,"<strong>$1</strong>")).replace(/^([A-ZÇĞİÖŞÜ][a-zçğıöşü\s]+:)/gm,"<strong>$1</strong>")).replace(/\*(.+?)\*/g,"<em>$1</em>")).replace(/"([^"]+)"/g,'<em>"$1"</em>')).replace(/^[\s]*[-•]\s*(.+)$/gm,'<li class="mb-2">$1</li>')).replace(/^[\s]*\d+\.\s*(.+)$/gm,'<li class="mb-2">$1</li>')).replace(/(<li class="mb-2">.*?<\/li>)(\s*<li class="mb-2">.*?<\/li>)*/g,e=>'<ul class="list-disc pl-6 mb-4">'+e+"</ul>")).replace(/\n\s*\n/g,'</p><p class="mb-4">')).startsWith("<")||(t='<p class="mb-4">'+t+"</p>")),d(t)},o=e=>{let t=(e=e.replace(/\|([^|]+\|[^|]+.*)\|/g,(e,t)=>{let s=t.split("|").map(e=>e.trim()).filter(e=>e);return s.length>=2?`
        <div class="overflow-x-auto mb-6">
          <table class="min-w-full border border-gray-300">
            <tbody>
              <tr>
                ${s.map(e=>`<td class="border border-gray-300 px-4 py-2">${e}</td>`).join("")}
              </tr>
            </tbody>
          </table>
        </div>
      `:e})).split("\n"),s="",a=0;for(;a<t.length;){let e=t[a].trim();if(e.match(/^(Kriter|Gösterge|Senaryo|Kanal|Aktör|Dinamik)\s+(.+)$/)){let e=[],l=a;for(;l<t.length;){let s=t[l].trim();if(!s){l++;continue}let a=s.split(/\s{2,}/).filter(e=>e.trim());if(a.length>=2)e.push(a),l++;else break}if(e.length>=2){s+='<div class="overflow-x-auto mb-6">\n<table class="min-w-full border border-gray-300">\n<thead>\n<tr>\n',e[0].forEach(e=>{s+=`<th class="border border-gray-300 px-4 py-2 bg-gray-100 text-left font-semibold">${e}</th>
`}),s+="</tr>\n</thead>\n<tbody>\n";for(let t=1;t<e.length;t++)s+="<tr>\n",e[t].forEach(e=>{s+=`<td class="border border-gray-300 px-4 py-2">${e}</td>
`}),s+="</tr>\n";s+="</tbody>\n</table>\n</div>\n",a=l;continue}}if(e.match(/^[A-ZÇĞİÖŞÜ][^:]+\s+%?\d+[%\d\s\w]*\s+[A-ZÇĞİÖŞÜ]/)){let t=e.split(/\s{2,}/).filter(e=>e.trim());if(t.length>=3){s+=`
          <div class="overflow-x-auto mb-4">
            <table class="min-w-full border border-gray-300">
              <tbody>
                <tr>
                  ${t.map(e=>`<td class="border border-gray-300 px-4 py-2">${e}</td>`).join("")}
                </tr>
              </tbody>
            </table>
          </div>
        `,a++;continue}}s+=e+"\n",a++}return s},d=e=>e=o(e=(e=(e=(e=(e=(e=(e=(e=(e=e.replace(/<p class="mb-4">\s*<\/p>/g,"")).replace(/<p class="mb-4"><\/p>/g,"")).replace(/<p>\s*<\/p>/g,"")).replace(/<\/p>\s*(<h[1-6])/g,"</p>\n$1")).replace(/(<\/h[1-6]>)\s*<p class="mb-4">/g,'$1\n<p class="mb-4">')).replace(/<\/p>\s*<ul/g,"</p>\n<ul")).replace(/<\/ul>\s*<p/g,"</ul>\n<p")).replace(/<\/p>\s*<div/g,"</p>\n<div")).replace(/<\/div>\s*<p/g,"</div>\n<p"));function c(){let[e,t]=(0,i.useState)([]),[s,o]=(0,i.useState)(!0),[d,c]=(0,i.useState)(1),[m,x]=(0,i.useState)(1),[h,b]=(0,i.useState)(null);(0,i.useEffect)(()=>{g()},[d]);let g=async()=>{try{o(!0);let e=(d-1)*12,s=new URLSearchParams({site_code:"mgsam",limit:"12",offset:e.toString()}),a=await fetch(`/api/articles.php?${s}`);if(a.ok){let e=await a.json();e.success&&e.data?(t(e.data.articles||[]),x(Math.ceil((e.data.total||0)/12))):t([])}else t([]),x(1)}catch(e){t([]),x(1)}finally{o(!1)}},p=async e=>{try{o(!0);let t=await fetch(`/api/articles.php?slug=${e.slug}&site_code=mgsam`);if(t.ok){let e=await t.json();e.success&&e.data&&(e.data.id?b(e.data):e.data.articles&&e.data.articles.length>0&&b(e.data.articles[0]))}}catch(e){}finally{o(!1)}},u=e=>{c(e),window.scrollTo({top:0,behavior:"smooth"})};return s?(0,a.jsxs)("main",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50",children:[(0,a.jsx)(l.A,{currentPage:"makalelerimiz"}),(0,a.jsx)("div",{className:"pt-32 pb-16",children:(0,a.jsx)("div",{className:"container mx-auto px-6",children:(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Makaleler y\xfckleniyor..."})]})})}),(0,a.jsx)(r.A,{})]}):h?(0,a.jsxs)("main",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)(l.A,{}),(0,a.jsxs)("section",{className:"relative bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 py-20",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black/20"}),(0,a.jsx)("div",{className:"container mx-auto px-6 relative z-10",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsxs)("button",{onClick:()=>b(null),className:"inline-flex items-center px-6 py-3 bg-white/10 text-white rounded-lg hover:bg-white/20 transition-all duration-300 border border-white/20",children:[(0,a.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Makalelere D\xf6n"]})}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"inline-flex items-center px-4 py-2 bg-white/10 rounded-full text-white text-sm font-medium mb-6 border border-white/20",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-blue-500 rounded-full mr-3"}),"Makalelerimiz"]}),(0,a.jsx)("h1",{className:"text-xl md:text-3xl font-light text-white mb-6 leading-snug md:leading-normal",children:h.title}),(0,a.jsx)("div",{className:"flex items-center justify-center space-x-6 text-white/80",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})}),(0,a.jsx)("span",{children:h.author})]})})]})]})})]}),(0,a.jsx)("section",{className:"py-16 bg-white",children:(0,a.jsx)("div",{className:"container mx-auto px-6",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[h.content?(0,a.jsx)("div",{className:"max-w-none text-gray-800 leading-relaxed article-content",style:{lineHeight:"1.7",fontSize:"16px"},dangerouslySetInnerHTML:{__html:n(h.content).replace(/(https?:\/\/[^\s)]+)/g,'<a href="$1" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:text-blue-800 underline">$1</a>')}}):(0,a.jsx)("div",{className:"text-center py-12",children:(0,a.jsxs)("div",{className:"text-gray-500",children:[(0,a.jsx)("h3",{className:"text-xl font-medium mb-4",children:"Makale İ\xe7eriği Y\xfckleniyor..."}),(0,a.jsx)("p",{children:"Makale i\xe7eriği hen\xfcz y\xfcklenemedi. L\xfctfen daha sonra tekrar deneyin."})]})}),(0,a.jsx)("div",{className:"text-center mt-16",children:(0,a.jsxs)("button",{onClick:()=>b(null),className:"inline-flex items-center px-8 py-4 bg-gradient-to-r from-slate-800 to-slate-900 text-white rounded-lg hover:from-slate-900 hover:to-black transition-all duration-300 shadow-lg hover:shadow-xl",children:[(0,a.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Diğer Makaleleri G\xf6r\xfcnt\xfcle"]})})]})})}),(0,a.jsx)(r.A,{})]}):(0,a.jsxs)("main",{className:"min-h-screen",children:[(0,a.jsx)(l.A,{currentPage:"makalelerimiz"}),(0,a.jsx)("section",{className:"pt-32 pb-16 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700",children:(0,a.jsx)("div",{className:"container mx-auto px-6",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto text-center text-white",children:[(0,a.jsxs)("div",{className:"inline-flex items-center px-4 py-2 bg-white/10 rounded-full text-white text-sm font-medium mb-6 border border-white/20",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-blue-500 rounded-full mr-3"}),"Araştırma Makalelerimiz"]}),(0,a.jsx)("h1",{className:"text-4xl md:text-6xl font-light mb-6 text-white",children:"Makalelerimiz"}),(0,a.jsx)("p",{className:"text-xl text-white/90 mb-8 max-w-2xl mx-auto",children:"Meta Global Stratejiler Ekonomik ve Siyasal Araştırmalar Merkezi'nin derinlemesine analiz ve araştırma makaleleri"})]})})}),(0,a.jsx)("section",{className:"py-24 bg-white",children:(0,a.jsx)("div",{className:"container mx-auto px-6",children:(0,a.jsxs)("div",{className:"max-w-6xl mx-auto",children:[0===e.length?(0,a.jsx)("div",{className:"text-center py-16",children:(0,a.jsxs)("div",{className:"text-gray-500",children:[(0,a.jsx)("h3",{className:"text-2xl font-light mb-4",children:"Hen\xfcz makale bulunmuyor"}),(0,a.jsx)("p",{className:"text-lg",children:"Yakında yeni makaleler eklenecektir."})]})}):(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6",children:e.map(e=>(0,a.jsxs)("article",{onClick:()=>p(e),onKeyDown:t=>{("Enter"===t.key||" "===t.key)&&(t.preventDefault(),p(e))},tabIndex:0,role:"button","aria-label":`${e.title} makalesini oku`,className:"group bg-white rounded-xl shadow-sm hover:shadow-xl transition-all duration-500 hover:-translate-y-2 overflow-hidden border border-gray-100 cursor-pointer focus:outline-none focus:ring-2 focus:ring-slate-500 focus:ring-offset-2",children:[(0,a.jsx)("div",{className:"h-1.5 bg-slate-900"}),(0,a.jsxs)("div",{className:"p-4 md:p-6",children:[(0,a.jsx)("div",{className:"mb-3 md:mb-4",children:(0,a.jsxs)("div",{className:"inline-flex items-center px-2 md:px-3 py-1 md:py-1.5 bg-slate-50 text-slate-700 text-xs md:text-sm font-medium rounded-full border border-slate-200 w-fit",children:[(0,a.jsx)("span",{className:"w-1.5 md:w-2 h-1.5 md:h-2 bg-slate-600 rounded-full mr-1.5 md:mr-2"}),(0,a.jsx)("span",{className:"truncate",children:e.category})]})}),(0,a.jsx)("h2",{className:"text-sm md:text-lg font-semibold text-gray-900 mb-3 md:mb-4 group-hover:text-slate-700 transition-colors duration-300 leading-tight min-h-[3rem] md:min-h-[3.5rem] flex items-start",children:e.title}),(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsx)("div",{className:"text-xs text-gray-500",children:(0,a.jsx)("span",{className:"font-medium",children:e.author})})}),(0,a.jsx)("div",{className:"pt-3 md:pt-4 border-t border-gray-100",children:(0,a.jsxs)("div",{className:"flex items-center justify-center md:justify-end text-slate-600 text-xs md:text-sm font-medium group-hover:text-slate-800 transition-colors",children:[(0,a.jsx)("span",{className:"hidden md:inline",children:"Devamını Oku"}),(0,a.jsx)("span",{className:"md:hidden",children:"Oku"}),(0,a.jsx)("svg",{className:"w-3 md:w-4 h-3 md:h-4 ml-1 group-hover:translate-x-1 transition-transform",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})})]})]},e.id))}),m>1&&(0,a.jsxs)("div",{className:"flex justify-center items-center space-x-4 mt-16",children:[(0,a.jsx)("button",{onClick:()=>u(d-1),disabled:1===d,className:"px-6 py-3 bg-slate-800 hover:bg-slate-900 text-white rounded-lg transition-all duration-300 font-medium shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed",children:"← \xd6nceki"}),(0,a.jsxs)("span",{className:"text-gray-600 font-medium",children:["Sayfa ",d," / ",m]}),(0,a.jsx)("button",{onClick:()=>u(d+1),disabled:d===m,className:"px-6 py-3 bg-slate-800 hover:bg-slate-900 text-white rounded-lg transition-all duration-300 font-medium shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed",children:"Sonraki →"})]})]})})}),(0,a.jsx)(r.A,{})]})}},3965:(e,t,s)=>{Promise.resolve().then(s.bind(s,206))}},e=>{var t=t=>e(e.s=t);e.O(0,[766,874,49,441,684,358],()=>t(3965)),_N_E=e.O()}]);