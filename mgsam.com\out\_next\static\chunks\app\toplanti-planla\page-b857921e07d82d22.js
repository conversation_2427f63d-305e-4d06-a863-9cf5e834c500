(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[925],{5202:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>n});var t=a(5155),l=a(2115),r=a(7864),i=a(5922);function n(){let[e,s]=(0,l.useState)({name:"",email:"",phone:"",company:"",service:"",date:"",time:"",message:"",notes:""}),[a,n]=(0,l.useState)(!1),[d,o]=(0,l.useState)(""),[c,m]=(0,l.useState)(!1),x=async a=>{a.preventDefault(),n(!0),o("");try{let a=await fetch("/api/meeting.php",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...e,meeting_date:e.date,meeting_time:e.time})}),t=await a.json();t.success?(m(!0),o(t.message),s({name:"",email:"",phone:"",company:"",service:"",date:"",time:"",message:"",notes:""})):(m(!1),o(t.message))}catch(e){m(!1),o("Bir hata oluştu. L\xfctfen daha sonra tekrar deneyin.")}finally{n(!1)}},u=a=>{s({...e,[a.target.name]:a.target.value})};return(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-white flex flex-col",children:[(0,t.jsx)(r.A,{currentPage:"toplanti-planla"}),(0,t.jsx)("main",{className:"flex-1 pt-20 pb-8",children:(0,t.jsx)("div",{className:"container mx-auto px-4",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto flex flex-col",children:[(0,t.jsxs)("div",{className:"text-center py-8 hidden md:block",children:[(0,t.jsxs)("div",{className:"inline-flex items-center px-3 py-1 bg-slate-100 rounded-full text-slate-700 text-sm font-medium mb-3",children:[(0,t.jsx)("span",{className:"w-2 h-2 bg-gradient-to-r from-green-500 to-green-400 rounded-full mr-2"})," ","\xdccretsiz Danışmanlık Toplantısı"]}),(0,t.jsxs)("h1",{className:"text-3xl md:text-4xl font-extralight text-slate-900 mb-2 tracking-tight",children:["Toplantı"," ",(0,t.jsx)("span",{className:"text-slate-700 font-light",children:"Planlayın"})]}),(0,t.jsx)("p",{className:"text-slate-600 font-light text-sm",children:"Uzman ekibimizle randevu alın ve projelerinizi değerlendirelim"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8 py-8",children:[(0,t.jsx)("div",{className:"lg:col-span-2 lg:order-2",children:(0,t.jsxs)("div",{className:"bg-white rounded-3xl p-6 shadow-lg border border-slate-200/50 relative overflow-hidden",children:[(0,t.jsx)("div",{className:"absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-slate-100/30 to-transparent rounded-full -translate-y-16 translate-x-16"}),(0,t.jsxs)("div",{className:"relative z-10",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h2",{className:"text-2xl font-light text-slate-900 mb-2 tracking-tight",children:"Randevu Formu"}),(0,t.jsx)("p",{className:"text-slate-600 text-sm",children:"Detaylı bilgilerinizi paylaşın, size en uygun zamanı belirleyelim"})]}),(0,t.jsxs)("form",{onSubmit:x,className:"flex flex-col space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:[(0,t.jsx)("div",{children:(0,t.jsx)("input",{type:"text",name:"name",placeholder:"Adınız Soyadınız *",value:e.name,onChange:u,required:!0,className:"w-full px-4 py-2.5 border border-slate-200 rounded-xl focus:ring-2 focus:ring-slate-400 focus:border-transparent transition-all duration-200 text-sm"})}),(0,t.jsx)("div",{children:(0,t.jsx)("input",{type:"email",name:"email",placeholder:"E-posta Adresiniz *",value:e.email,onChange:u,required:!0,className:"w-full px-4 py-2.5 border border-slate-200 rounded-xl focus:ring-2 focus:ring-slate-400 focus:border-transparent transition-all duration-200 text-sm"})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:[(0,t.jsx)("div",{children:(0,t.jsx)("input",{type:"tel",name:"phone",placeholder:"Telefon Numaranız *",value:e.phone,onChange:u,required:!0,className:"w-full px-4 py-2.5 border border-slate-200 rounded-xl focus:ring-2 focus:ring-slate-400 focus:border-transparent transition-all duration-200 text-sm"})}),(0,t.jsx)("div",{children:(0,t.jsx)("input",{type:"text",name:"company",placeholder:"Şirket Adınız *",value:e.company,onChange:u,required:!0,className:"w-full px-4 py-2.5 border border-slate-200 rounded-xl focus:ring-2 focus:ring-slate-400 focus:border-transparent transition-all duration-200 text-sm"})})]}),(0,t.jsx)("div",{children:(0,t.jsxs)("select",{name:"service",value:e.service,onChange:u,required:!0,className:"w-full px-4 py-2.5 border border-slate-200 rounded-xl focus:ring-2 focus:ring-slate-400 focus:border-transparent transition-all duration-200 text-sm",children:[(0,t.jsx)("option",{value:"",children:"Hizmet Alanı Se\xe7iniz *"}),["Stratejik Danışmanlık","İş Geliştirme","Dijital D\xf6n\xfcş\xfcm","Proje Y\xf6netimi","Eğitim ve Gelişim","Diğer"].map((e,s)=>(0,t.jsx)("option",{value:e,children:e},`hizmet-${e.slice(0,10)}-${s}`))]})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:[(0,t.jsx)("div",{children:(0,t.jsx)("input",{type:"date",name:"date",value:e.date,onChange:u,required:!0,min:new Date().toISOString().split("T")[0],className:"w-full px-4 py-2.5 border border-slate-200 rounded-xl focus:ring-2 focus:ring-slate-400 focus:border-transparent transition-all duration-200 text-sm"})}),(0,t.jsx)("div",{children:(0,t.jsx)("input",{type:"time",name:"time",value:e.time,onChange:u,required:!0,className:"w-full px-4 py-2.5 border border-slate-200 rounded-xl focus:ring-2 focus:ring-slate-400 focus:border-transparent transition-all duration-200 text-sm"})})]}),(0,t.jsx)("div",{children:(0,t.jsx)("textarea",{name:"message",placeholder:"Toplantıda konuşmak istediğiniz konular *",value:e.message,onChange:u,required:!0,rows:4,className:"w-full px-4 py-2.5 border border-slate-200 rounded-xl focus:ring-2 focus:ring-slate-400 focus:border-transparent transition-all duration-200 text-sm resize-none"})}),d&&(0,t.jsx)("div",{className:`p-3 rounded-xl text-sm font-medium ${c?"bg-green-50 text-green-700 border border-green-200":"bg-red-50 text-red-700 border border-red-200"}`,children:d}),(0,t.jsxs)("button",{type:"submit",disabled:a,className:`w-full py-2.5 px-6 rounded-xl font-medium transition-all duration-300 transform shadow-lg flex items-center justify-center text-sm ${a?"bg-slate-400 text-white cursor-not-allowed":"bg-gradient-to-r from-slate-900 to-slate-700 text-white hover:from-slate-800 hover:to-slate-600 hover:-translate-y-1 hover:shadow-xl"}`,children:[(0,t.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})}),a?"G\xf6nderiliyor...":"Toplantı Talebini G\xf6nder"]})]})]})]})}),(0,t.jsxs)("div",{className:"lg:col-span-1 lg:order-1 space-y-4",children:[(0,t.jsxs)("div",{className:"bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700 rounded-3xl p-5 text-white relative overflow-hidden",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width=%2240%22 height=%2240%22 viewBox=%220 0 40 40%22 xmlns=%22http://www.w3.org/2000/svg%22%3E%3Cg fill=%22none%22 fill-rule=%22evenodd%22%3E%3Cg fill=%22%23ffffff%22 fill-opacity=%220.03%22%3E%3Cpath d=%22m0 40l40-40h-40z%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-30"}),(0,t.jsxs)("div",{className:"relative z-10",children:[(0,t.jsxs)("h3",{className:"font-medium text-white mb-3 flex items-center text-sm",children:[(0,t.jsx)("svg",{className:"w-5 h-5 text-slate-200 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})}),"Neler Konuşacağız?"]}),(0,t.jsxs)("ul",{className:"space-y-2 text-sm text-slate-300",children:[(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"w-1.5 h-1.5 bg-slate-300 rounded-full mr-3 mt-1.5 flex-shrink-0"}),"İhtiya\xe7larınızı analiz edeceğiz"]}),(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"w-1.5 h-1.5 bg-slate-300 rounded-full mr-3 mt-1.5 flex-shrink-0"}),"\xd6zel \xe7\xf6z\xfcm \xf6nerilerimizi paylaşacağız"]}),(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"w-1.5 h-1.5 bg-slate-300 rounded-full mr-3 mt-1.5 flex-shrink-0"}),"Proje s\xfcrecini planlayacağız"]}),(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"w-1.5 h-1.5 bg-slate-300 rounded-full mr-3 mt-1.5 flex-shrink-0"}),"Sorularınızı yanıtlayacağız"]})]})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-3xl p-5 shadow-lg border border-slate-200/50",children:[(0,t.jsx)("h3",{className:"font-medium text-slate-900 mb-3 text-sm",children:"Hızlı İletişim"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("svg",{className:"w-4 h-4 text-slate-500 mr-3",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})}),"+90 (542) 797 05 00"]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("svg",{className:"w-4 h-4 text-slate-500 mr-3",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})}),"<EMAIL>"]})]})]})]})]})]})})}),(0,t.jsx)(i.A,{})]})}},5981:(e,s,a)=>{Promise.resolve().then(a.bind(a,5202))}},e=>{var s=s=>e(e.s=s);e.O(0,[766,874,49,441,684,358],()=>s(5981)),_N_E=e.O()}]);