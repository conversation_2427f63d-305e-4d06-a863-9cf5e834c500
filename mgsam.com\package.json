{"name": "metaanalizgorup_website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "analyze": "cross-env ANALYZE=true npm run build", "build:production": "npm run type-check && npm run lint && npm run build", "css:build": "tailwindcss -i ./app/globals.css -o ./public/globals.css --watch"}, "dependencies": {"@types/jsonwebtoken": "^9.0.10", "bcryptjs": "^3.0.2", "clsx": "^2.1.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.523.0", "mysql2": "^3.14.1", "next": "^15.3.4", "next-auth": "^4.24.11", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.0.1", "sharp": "^0.34.2", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@types/json5": "^2.2.0", "@types/node": "^20.19.1", "@types/react": "^18.3.23", "@types/react-dom": "^18", "autoprefixer": "^10.4.21", "critters": "^0.0.25", "cross-env": "^7.0.3", "eslint": "^8", "eslint-config-next": "14.1.0", "postcss": "^8.5.6", "postcss-import": "^16.1.1", "tailwindcss": "^3.4.0", "typescript": "^5", "webpack-bundle-analyzer": "^4.10.2"}, "browserslist": ["> 0.5%", "last 2 versions", "not dead", "not ie 11", "not op_mini all", "Chrome >= 88", "Firefox >= 85", "Safari >= 14", "Edge >= 88"]}